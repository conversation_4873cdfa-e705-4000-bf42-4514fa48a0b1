const { momentNow } = require("../helpers");
const { FirestoreRef, CONSTANTS, COLLECTIONS } = require("../init");
// ✅ CORREÇÃO: Usar cliente Redis seguro e otimizado
const {
  getScheduledEmails,
  removeProcessedEmail,
  getEmailQueueStats,
  testRedisPerformance,
} = require("../utils/redisClient");

/**
 * Sistema de Envio de Emails Escalável v2.0
 *
 * ✅ CORREÇÕES IMPLEMENTADAS NA TASK 2:
 * - Configuração Redis segura via Firebase Config
 * - SSL/TLS habilitado para conexões seguras
 * - Remoção adequada de emails processados
 * - Connection pooling otimizado
 * - Logging estruturado e observabilidade
 * - Validação rigorosa de configurações
 *
 * <AUTHOR> Team
 * @version 2.0.0 - Redis Seguro
 */

/**
 * Função principal do sistema de email queue
 * Implementação focada na Task 2: Configuração Redis Segura
 *
 * ✅ CORREÇÕES IMPLEMENTADAS:
 * - Integração com cliente Redis seguro
 * - Remoção adequada de emails processados
 * - Logging estruturado
 * - Gestão de erros melhorada
 *
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Tamanho do lote (default: 100)
 * @param {boolean} options.dryRun - Modo de simulação (default: false)
 * @param {string} options.queueKey - Chave da fila Redis (default: 'email_queue:scheduled')
 * @returns {Promise<Object>} Resultado do processamento
 */
const emailQueue = async (options = {}) => {
  const startTime = Date.now();
  const {
    batchSize = 100,
    dryRun = false,
    queueKey = "email_queue:scheduled",
  } = options;

  try {
    console.log("EMAIL_QUEUE > SYSTEM > START", {
      timestamp: momentNow().format(CONSTANTS.MOMENT_ISO),
      batchSize,
      dryRun,
      queueKey,
    });

    // ✅ CORREÇÃO: Usar cliente Redis seguro para buscar emails
    const scheduledEmails = await getScheduledEmails(batchSize, queueKey);

    if (scheduledEmails.length === 0) {
      console.log("EMAIL_QUEUE > SYSTEM > NO_EMAILS_TO_PROCESS");
      return {
        success: true,
        processed: 0,
        errors: 0,
        skipped: 0,
        duration: Date.now() - startTime,
      };
    }

    console.log("EMAIL_QUEUE > SYSTEM > PROCESSING", {
      emailsFound: scheduledEmails.length,
      batchSize,
    });

    // Processar emails (implementação simplificada para Task 2)
    let processed = 0;
    let errors = 0;
    let skipped = 0;

    for (const email of scheduledEmails) {
      try {
        if (dryRun) {
          console.log("EMAIL_QUEUE > DRY_RUN", {
            emailId: email.id,
            to: email.to,
            subject: email.subject,
          });
          skipped++;
        } else {
          // Simular processamento (será implementado nas próximas tasks)
          console.log("EMAIL_QUEUE > PROCESS", {
            emailId: email.id,
            to: email.to,
            subject: email.subject,
          });

          // ✅ CORREÇÃO: Remover email processado do Redis
          const removed = await removeProcessedEmail(email.redis_key, queueKey);
          if (removed) {
            processed++;
            console.log("EMAIL_QUEUE > REMOVED", { emailId: email.id });
          } else {
            console.warn("EMAIL_QUEUE > REMOVE_FAILED", { emailId: email.id });
          }
        }
      } catch (emailError) {
        console.error("EMAIL_QUEUE > EMAIL_ERROR", {
          emailId: email.id,
          error: emailError.message,
        });
        errors++;
      }
    }

    const duration = Date.now() - startTime;

    console.log("EMAIL_QUEUE > SYSTEM > COMPLETE", {
      processed,
      errors,
      skipped,
      duration: `${duration}ms`,
    });

    return {
      success: true,
      processed,
      errors,
      skipped,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;

    console.error("EMAIL_QUEUE > SYSTEM > ERROR", {
      error: error.message,
      stack: error.stack,
      duration: `${duration}ms`,
    });

    return {
      success: false,
      error: error.message,
      processed: 0,
      errors: 1,
      skipped: 0,
      duration,
    };
  }
};

/**
 * ✅ FUNÇÕES UTILITÁRIAS PARA SISTEMA DE EMAIL ESCALÁVEL
 * Implementadas com foco na Task 2: Configuração Redis Segura
 */

/**
 * Função de conveniência para processar emails com configurações padrão
 * Usa o cliente Redis seguro implementado na Task 2
 *
 * @returns {Promise<Object>} Resultado do processamento
 */
const processScheduledEmails = async () => {
  return await emailQueue({
    batchSize: 100,
    dryRun: false,
    queueKey: "email_queue:scheduled",
  });
};

/**
 * Função para processar emails em modo de simulação
 * Útil para testes e validação das configurações Redis
 *
 * @param {Object} options - Opções adicionais
 * @returns {Promise<Object>} Resultado da simulação
 */
const simulateEmailProcessing = async (options = {}) => {
  return await emailQueue({
    ...options,
    dryRun: true,
    batchSize: options.batchSize || 10,
    queueKey: options.queueKey || "email_queue:scheduled",
  });
};

/**
 * Obtém estatísticas das filas de email
 * Usa as funções seguras do cliente Redis
 *
 * @returns {Promise<Object>} Estatísticas das filas
 */
const getQueueStatistics = async () => {
  try {
    const stats = await getEmailQueueStats();

    console.log("EMAIL_QUEUE > STATS", stats);

    return {
      success: true,
      ...stats,
    };
  } catch (error) {
    console.error("EMAIL_QUEUE > STATS > ERROR", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Testa a performance do sistema Redis
 * Útil para validar as configurações implementadas na Task 2
 *
 * @returns {Promise<Object>} Resultado do teste
 */
const testSystemPerformance = async () => {
  try {
    const result = await testRedisPerformance();

    console.log("EMAIL_QUEUE > PERFORMANCE_TEST", result);

    return result;
  } catch (error) {
    console.error("EMAIL_QUEUE > PERFORMANCE_TEST > ERROR", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Função para agendar um email usando o sistema Redis seguro
 * Demonstra o uso das configurações implementadas na Task 2
 *
 * @param {Object} emailData - Dados do email
 * @param {string} scheduledDate - Data de agendamento
 * @param {string} queueKey - Chave da fila (opcional)
 * @returns {Promise<Object>} Resultado do agendamento
 */
const scheduleEmailForProcessing = async (
  emailData,
  scheduledDate,
  queueKey = "email_queue:scheduled"
) => {
  try {
    // Importar função de agendamento do cliente Redis
    const { scheduleEmail } = require("../utils/redisClient");

    const emailId = await scheduleEmail(emailData, scheduledDate, queueKey);

    if (emailId) {
      console.log("EMAIL_QUEUE > SCHEDULE > SUCCESS", {
        emailId,
        to: emailData.to,
        scheduledDate,
        queueKey,
      });

      return {
        success: true,
        emailId,
        scheduledDate,
        queueKey,
      };
    } else {
      throw new Error("Failed to schedule email");
    }
  } catch (error) {
    console.error("EMAIL_QUEUE > SCHEDULE > ERROR", {
      error: error.message,
      emailData: { to: emailData.to, subject: emailData.subject },
    });

    return {
      success: false,
      error: error.message,
    };
  }
};

module.exports = {
  // Função principal
  emailQueue,

  // Funções de conveniência
  processScheduledEmails,
  simulateEmailProcessing,

  // Funções utilitárias
  getQueueStatistics,
  testSystemPerformance,
  scheduleEmailForProcessing,
};
