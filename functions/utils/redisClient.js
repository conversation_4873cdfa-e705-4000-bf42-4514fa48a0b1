const redis = require("redis");
const dotenv = require("dotenv");
const functions = require("firebase-functions");

// Carregar variáveis de ambiente do arquivo .env (fallback para desenvolvimento)
dotenv.config();

/**
 * Configurações Seguras do Redis
 *
 * CORREÇÕES IMPLEMENTADAS:
 * ✅ Configuração via Firebase Config (não mais .env hardcoded)
 * ✅ SSL/TLS habilitado para conexões seguras
 * ✅ Connection pooling otimizado
 * ✅ Validação rigorosa de configurações
 * ✅ Estratégia de reconexão melhorada
 *
 * @version 2.0.0 - Sistema Escalável
 */
const getSecureRedisConfig = () => {
  // Prioridade: Firebase Config > Environment Variables > Defaults
  const functionsConfig = functions.config() || {};
  const firebaseConfig = functionsConfig.redis || {};

  // Validar configurações obrigatórias
  const host = firebaseConfig.host || process.env.REDIS_HOST;
  const password = firebaseConfig.password || process.env.REDIS_PASSWORD;

  if (!host) {
    throw new Error(
      'REDIS_CONFIG > Host is required. Configure via: firebase functions:config:set redis.host="your-host"'
    );
  }

  if (!password) {
    console.warn(
      "REDIS_CONFIG > WARNING: No password configured. This is insecure for production!"
    );
  }

  const config = {
    socket: {
      host: host,
      port: parseInt(firebaseConfig.port || process.env.REDIS_PORT || "6379"),
      connectTimeout: parseInt(
        firebaseConfig.connect_timeout || process.env.REDIS_TIMEOUT || "10000"
      ),

      // ✅ CORREÇÃO: Estratégia de reconexão melhorada com jitter
      reconnectStrategy: (retries) => {
        if (retries > 10) {
          console.error(
            `REDIS_CONFIG > Max reconnection attempts (10) reached`
          );
          return false; // Para de tentar reconectar
        }

        // Exponential backoff com jitter para evitar thundering herd
        const baseDelay = Math.pow(2, retries) * 100;
        const jitter = Math.random() * 1000; // Adicionar até 1s de jitter
        const delay = Math.min(baseDelay + jitter, 30000); // Máximo 30s

        console.log(
          `REDIS_CONFIG > Reconnection attempt ${retries + 1}/10, delay: ${Math.round(delay)}ms`
        );
        return delay;
      },

      // ✅ CORREÇÃO: SSL/TLS habilitado para segurança
      tls:
        firebaseConfig.ssl === "true" || process.env.REDIS_SSL === "true"
          ? {
              rejectUnauthorized: false, // Para certificados auto-assinados
              requestCert: true,
              agent: false,
            }
          : undefined,
    },

    // ✅ CORREÇÃO: Senha obrigatória para segurança
    password: password,
    database: parseInt(
      firebaseConfig.database || process.env.REDIS_DATABASE || "0"
    ),

    // ✅ CORREÇÃO: Configurações otimizadas para performance e estabilidade
    disableOfflineQueue: false,
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableAutoPipelining: true, // Melhora performance
    lazyConnect: true, // Conecta apenas quando necessário
    keepAlive: 30000, // Keep-alive para conexões estáveis
    family: 4, // IPv4

    // ✅ CORREÇÃO: Timeouts configuráveis
    commandTimeout: parseInt(firebaseConfig.command_timeout || "5000"),
    connectTimeout: parseInt(firebaseConfig.connect_timeout || "10000"),
  };

  // Log de configuração (sem dados sensíveis)
  console.log("REDIS_CONFIG > Secure configuration loaded", {
    host: config.socket.host,
    port: config.socket.port,
    database: config.database,
    ssl: !!config.socket.tls,
    hasPassword: !!config.password,
  });

  return config;
};

// Variável para armazenar a instância do cliente Redis
let redisClient = null;

/**
 * Inicializa e retorna um cliente Redis com configurações seguras
 *
 * ✅ CORREÇÕES IMPLEMENTADAS:
 * - Configuração via Firebase Config
 * - SSL/TLS habilitado
 * - Validação de configurações
 * - Connection pooling otimizado
 *
 * @returns {Promise<Object|null>} Cliente Redis ou null em caso de erro
 */
const getRedisClient = async () => {
  // Se já existe um cliente conectado, retorná-lo
  if (redisClient && redisClient.isOpen) {
    return redisClient;
  }

  // Se existe um cliente mas não está conectado, tentar reconectar
  if (redisClient && !redisClient.isOpen) {
    try {
      console.log(
        "REDIS_CLIENT > RECONNECT > Attempting to reconnect existing client"
      );
      await redisClient.connect();
      return redisClient;
    } catch (error) {
      console.error(
        "REDIS_CLIENT > RECONNECT > Failed to reconnect existing client:",
        error.message
      );
      // Continuar para criar um novo cliente
      redisClient = null;
    }
  }

  try {
    // ✅ CORREÇÃO: Usar configuração segura
    const REDIS_CONFIG = getSecureRedisConfig();

    console.log(
      `REDIS_CLIENT > CONNECT > Creating secure connection to ${REDIS_CONFIG.socket.host}:${REDIS_CONFIG.socket.port}`
    );

    // Criar um novo cliente Redis com as configurações seguras
    redisClient = redis.createClient(REDIS_CONFIG);

    // ✅ CORREÇÃO: Handlers de eventos melhorados para observabilidade
    redisClient.on("error", (error) => {
      console.error("REDIS_CLIENT > ERROR", {
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString(),
      });

      // Tratamento específico para erros comuns com ações sugeridas
      if (error.message.includes("NOAUTH")) {
        console.error(
          "REDIS_CLIENT > AUTH_ERROR > Check Firebase Config: firebase functions:config:set redis.password='your-password'"
        );
      } else if (error.message.includes("ECONNREFUSED")) {
        console.error(
          "REDIS_CLIENT > CONNECTION_ERROR > Server unreachable. Check host and port configuration."
        );
      } else if (error.message.includes("ETIMEDOUT")) {
        console.error(
          "REDIS_CLIENT > TIMEOUT_ERROR > Connection timed out. Check network connectivity."
        );
      } else if (error.message.includes("ENOTFOUND")) {
        console.error(
          "REDIS_CLIENT > DNS_ERROR > Host not found. Check redis.host configuration."
        );
      }
    });

    redisClient.on("connect", () => {
      console.log("REDIS_CLIENT > CONNECTED", {
        host: REDIS_CONFIG.socket.host,
        port: REDIS_CONFIG.socket.port,
        ssl: Boolean(REDIS_CONFIG.socket.tls),
        timestamp: new Date().toISOString(),
      });
    });

    redisClient.on("ready", () => {
      console.log(
        "REDIS_CLIENT > READY > Client authenticated and ready for operations"
      );
    });

    redisClient.on("reconnecting", (delay) => {
      console.log("REDIS_CLIENT > RECONNECTING", {
        delay: `${delay}ms`,
        timestamp: new Date().toISOString(),
      });
    });

    redisClient.on("end", () => {
      console.log("REDIS_CLIENT > DISCONNECTED > Connection closed gracefully");
      redisClient = null;
    });

    // Conectar ao servidor Redis
    await redisClient.connect();

    // Verificar se a conexão está realmente estabelecida
    if (!redisClient.isOpen) {
      throw new Error("Failed to establish connection");
    }

    console.log(
      "REDIS_CLIENT > READY > Connection established and ready for operations"
    );
    return redisClient;
  } catch (error) {
    console.error("REDIS_CLIENT > INIT_ERROR", {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });

    // Limpar a referência para permitir nova tentativa
    redisClient = null;
    return null;
  }
};

/**
 * Salva uma mensagem no Redis com um tempo de expiração
 * @param {string} key - Chave para armazenar a mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @param {number} expireInSeconds - Tempo de expiração em segundos (opcional)
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveMessage = async (key, message, expireInSeconds = null) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("SHOTXCRON > Redis client not available");
      return false;
    }

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar no Redis (API moderna usa apenas .set())
    const options = {};
    if (expireInSeconds && !isNaN(expireInSeconds)) {
      options.EX = expireInSeconds;
    }

    await client.set(key, messageStr, options);

    return true;
  } catch (error) {
    console.error("SHOTXCRON > Error saving message to Redis:", error);
    return false;
  }
};

/**
 * Salva uma mensagem em uma lista ordenada por tempo no Redis
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} score - Pontuação para ordenação (timestamp)
 * @param {string} messageKey - Chave única da mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveScheduledMessage = async (listKey, score, messageKey, message) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("SHOTXCRON > SAVEREDIS > Redis client not available");
      return false;
    }

    // Converter o score (timestamp) para uma data legível
    const scoreDate = new Date(score).toISOString();

    console.log(
      "SHOTXCRON > SAVEREDIS > BEFORE SAVE",
      "key",
      messageKey,
      "message",
      message,
      "listkey",
      listKey,
      "score",
      score,
      "scoreDate",
      scoreDate
    );

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar a mensagem com sua chave
    await client.set(messageKey, messageStr);

    // Adicionar à lista ordenada (API moderna usa .zAdd())
    await client.zAdd(listKey, [
      {
        score: score,
        value: messageKey,
      },
    ]);

    return true;
  } catch (error) {
    console.error(
      "SHOTXCRON > SAVEREDIS > Error saving scheduled message to Redis:",
      error
    );
    return false;
  }
};

/**
 * Obtém mensagens agendadas até um determinado timestamp
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} maxScore - Pontuação máxima (timestamp)
 * @param {Object} options - Opções adicionais
 * @param {boolean} options.remove - Se true, remove as mensagens da lista após obtê-las (default: true)
 * @param {number} options.limit - Número máximo de mensagens a retornar (default: 100)
 * @returns {Promise<Array>} - Lista de mensagens
 */
const getScheduledMessages = async (
  listKey,
  maxScore,
  origin = "",
  options = {}
) => {
  // Definir opções padrão
  const { remove = true, limit = 100 } = options;

  console.log("SHOTXCRON > GETREDIS > START > ORIGIN", {
    origin,
  });

  try {
    // Obter cliente Redis
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > GETREDIS > Client not available for getting scheduled messages"
      );
      return [];
    }

    // Validar parâmetros
    if (!listKey) {
      console.error(
        "SHOTXCRON > GETREDIS > Missing list key for scheduled messages"
      );
      return [];
    }

    if (!maxScore || isNaN(maxScore)) {
      console.error(
        "SHOTXCRON > GETREDIS > Invalid max score for scheduled messages"
      );
      return [];
    }

    console.log(
      `SHOTXCRON > GETREDIS > SEARCHING FOR MESSAGES SCHEDULED UNTIL (${new Date(maxScore).toISOString()})`
    );

    // Obter chaves das mensagens até o timestamp especificado com limite
    const messageKeys = await client.zRange(listKey, 0, maxScore, {
      BY: "SCORE",
      LIMIT: {
        offset: 0,
        count: limit,
      },
    });

    // Se não houver mensagens, retornar array vazio
    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > GETREDIS > No scheduled messages found");
      return [];
    }

    console.log(
      `SHOTXCRON > GETREDIS > Found ${messageKeys.length} scheduled messages to process`
    );

    // Obter as mensagens em lote usando pipeline para melhor performance
    const pipeline = client.multi();

    // Adicionar comandos GET para cada chave
    messageKeys.forEach((key) => {
      pipeline.get(key);
    });

    // Executar pipeline
    const messageResults = await pipeline.exec();

    // Processar resultados
    const messages = [];
    const failedKeys = [];
    const orphanedKeys = []; // Chaves que estão no sorted set mas não têm conteúdo

    console.log(
      `SHOTXCRON > GETREDIS > RESULTS > Processing ${messageResults.length} scheduled messages`
    );

    for (let i = 0; i < messageResults.length; i++) {
      const messageStr = messageResults[i];
      const key = messageKeys[i];

      if (messageStr) {
        try {
          const message = JSON.parse(messageStr);

          // Adicionar a chave Redis original à mensagem para facilitar a exclusão posterior
          message.redis_key = key;

          messages.push(message);

          console.log(
            `SHOTXCRON > GETREDIS > VALID MESSAGE > Key: ${key}, To: ${message.to || message.phone || "unknown"}`
          );
        } catch (e) {
          console.error(
            `SHOTXCRON > GETREDIS > Error parsing message with key ${key}:`,
            e.message
          );
          failedKeys.push(key);
        }
      } else {
        console.warn(
          `SHOTXCRON > GETREDIS > ORPHANED KEY > Key ${key} exists in sorted set but has no content`
        );
        orphanedKeys.push(key);
      }
    }

    // Limpar chaves órfãs (que estão no sorted set mas não têm conteúdo)
    if (orphanedKeys.length > 0) {
      console.log(
        `SHOTXCRON > GETREDIS > CLEANUP > Removing ${orphanedKeys.length} orphaned keys from sorted set`
      );

      try {
        await client.zRem(listKey, orphanedKeys);
        console.log(
          `SHOTXCRON > GETREDIS > CLEANUP > Successfully removed orphaned keys: ${orphanedKeys.join(", ")}`
        );
      } catch (cleanupError) {
        console.error(
          `SHOTXCRON > GETREDIS > CLEANUP > Error removing orphaned keys:`,
          cleanupError.message
        );
      }
    }

    console.log(
      `SHOTXCRON > GETREDIS > Successfully processed ${messages.length} messages`
    );
    return messages;
  } catch (error) {
    console.error(
      "SHOTXCRON > GETREDIS > Error getting scheduled messages:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Remove uma mensagem específica do Redis
 * @param {string} messageKey - Chave da mensagem a ser removida
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const removeMessage = async (
  messageKey,
  listKey = "shotx:scheduled_messages"
) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing message"
      );
      return false;
    }

    console.log(
      `SHOTXCRON > REDIS > REMOVE > Attempting to remove message ${messageKey} from list ${listKey}`
    );

    // Usar transação para garantir atomicidade
    const multi = client.multi();

    // Verificar se a mensagem existe no sorted set
    const scoreResult = await client.zScore(listKey, messageKey);
    console.log(
      `SHOTXCRON > REDIS > REMOVE > Message ${messageKey} score in sorted set: ${scoreResult}`
    );

    // Verificar se a chave da mensagem existe
    const exists = await client.exists(messageKey);
    console.log(
      `SHOTXCRON > REDIS > REMOVE > Message key ${messageKey} exists: ${exists}`
    );

    if (!exists && scoreResult === null) {
      console.warn(
        `SHOTXCRON > REDIS > REMOVE > Message ${messageKey} not found in Redis (already removed)`
      );
      return true; // Considerar como sucesso se já foi removida
    }

    // Remover da lista ordenada (sorted set)
    multi.zRem(listKey, messageKey);

    // Remover o conteúdo da mensagem
    multi.del(messageKey);

    // Executar transação
    const results = await multi.exec();

    console.log(
      `SHOTXCRON > REDIS > REMOVE > Transaction results for ${messageKey}:`,
      results
    );

    // Verificar se ambas as operações foram bem-sucedidas
    // O Redis retorna arrays no formato [error, result] para cada comando
    const zRemResult = results[0];
    const delResult = results[1];

    // Extrair os valores dos resultados
    const zRemCount = zRemResult && zRemResult.length > 1 ? zRemResult[1] : 0;
    const delCount = delResult && delResult.length > 1 ? delResult[1] : 0;

    // Verificar se houve erros
    const zRemError = zRemResult && zRemResult[0];
    const delError = delResult && delResult[0];

    if (zRemError) {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Error in zRem for ${messageKey}:`,
        zRemError.message
      );
    }

    if (delError) {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Error in del for ${messageKey}:`,
        delError.message
      );
    }

    // Considerar sucesso se pelo menos uma operação funcionou
    const zRemSuccess = !zRemError && zRemCount >= 0;
    const delSuccess = !delError && delCount >= 0;

    if (zRemSuccess || delSuccess) {
      console.log(
        `SHOTXCRON > REDIS > REMOVE > Successfully removed message ${messageKey} (zRem: ${zRemCount}, del: ${delCount})`
      );
      return true;
    } else {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Failed to remove message ${messageKey} (zRem: ${zRemCount}, del: ${delCount})`
      );
      return false;
    }
  } catch (error) {
    console.error(
      `SHOTXCRON > REDIS > REMOVE > Error removing message ${messageKey}:`,
      error.message
    );
    console.error(error.stack);
    return false;
  }
};

/**
 * Remove todas as mensagens do Redis
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const removeAllMessages = async (listKey = "shotx:scheduled_messages") => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing messages"
      );
      return 0;
    }

    // Obter todas as chaves de mensagens
    const messageKeys = await client.zRange(listKey, 0, -1);

    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > REDIS > No messages to remove");
      return 0;
    }

    console.log(`SHOTXCRON > REDIS > Removing ${messageKeys.length} messages`);

    // Remover todas as mensagens em lote
    if (messageKeys.length > 0) {
      // Remover as chaves da lista ordenada
      await client.zRem(listKey, messageKeys);

      // Remover o conteúdo de cada mensagem
      await client.del(messageKeys);
    }

    console.log(
      `SHOTXCRON > REDIS > Successfully removed ${messageKeys.length} messages`
    );
    return messageKeys.length;
  } catch (error) {
    console.error(
      "SHOTXCRON > REDIS > Error removing all messages:",
      error.message
    );
    return 0;
  }
};

/**
 * Diagnostica a integridade do Redis verificando chaves órfãs
 * @param {string} listKey - Chave da lista ordenada
 * @returns {Promise<Object>} - Relatório de diagnóstico
 */
const diagnoseRedisIntegrity = async (listKey = "shotx:scheduled_messages") => {
  try {
    const client = await getRedisClient();
    if (!client) {
      return { error: "Redis client not available" };
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Starting integrity check for ${listKey}`
    );

    // Obter todas as chaves do sorted set
    const sortedSetKeys = await client.zRange(listKey, 0, -1);

    if (!sortedSetKeys || sortedSetKeys.length === 0) {
      return {
        success: true,
        totalKeys: 0,
        validKeys: 0,
        orphanedKeys: [],
        missingContent: [],
        summary: "No keys found in sorted set",
      };
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Found ${sortedSetKeys.length} keys in sorted set`
    );

    // Verificar quais chaves têm conteúdo
    const pipeline = client.multi();
    sortedSetKeys.forEach((key) => {
      pipeline.exists(key);
    });

    const existsResults = await pipeline.exec();

    const validKeys = [];
    const orphanedKeys = [];

    for (let i = 0; i < sortedSetKeys.length; i++) {
      const key = sortedSetKeys[i];
      const exists = existsResults[i] && existsResults[i][1] === 1;

      if (exists) {
        validKeys.push(key);
      } else {
        orphanedKeys.push(key);
      }
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Valid keys: ${validKeys.length}, Orphaned keys: ${orphanedKeys.length}`
    );

    // Limpar chaves órfãs se encontradas
    if (orphanedKeys.length > 0) {
      console.log(
        `SHOTXCRON > DIAGNOSE > Cleaning up ${orphanedKeys.length} orphaned keys`
      );
      await client.zRem(listKey, orphanedKeys);
    }

    return {
      success: true,
      totalKeys: sortedSetKeys.length,
      validKeys: validKeys.length,
      orphanedKeys: orphanedKeys.length,
      orphanedKeysList: orphanedKeys,
      cleanedUp: orphanedKeys.length > 0,
      summary: `Found ${validKeys.length} valid and ${orphanedKeys.length} orphaned keys`,
    };
  } catch (error) {
    console.error(
      "SHOTXCRON > DIAGNOSE > Error during integrity check:",
      error.message
    );
    return {
      success: false,
      error: error.message,
      summary: "Integrity check failed",
    };
  }
};

/**
 * Força a limpeza de chaves órfãs em uma lista ordenada
 * @param {string} listKey - Chave da lista ordenada
 * @returns {Promise<number>} - Número de chaves órfãs removidas
 */
const cleanupOrphanedKeys = async (listKey = "shotx:scheduled_messages") => {
  try {
    const diagnosis = await diagnoseRedisIntegrity(listKey);

    if (diagnosis.success && diagnosis.orphanedKeys > 0) {
      console.log(
        `SHOTXCRON > CLEANUP > Removed ${diagnosis.orphanedKeys} orphaned keys from ${listKey}`
      );
      return diagnosis.orphanedKeys;
    }

    return 0;
  } catch (error) {
    console.error("SHOTXCRON > CLEANUP > Error during cleanup:", error.message);
    return 0;
  }
};

/**
 * ✅ NOVAS FUNÇÕES PARA SISTEMA DE EMAIL ESCALÁVEL
 * Funções específicas para o novo sistema de email queue
 */

/**
 * Agenda um email no Redis usando configurações otimizadas
 *
 * @param {Object} emailData - Dados do email
 * @param {string} scheduledDate - Data de agendamento (ISO string)
 * @param {string} queueKey - Chave da fila (default: 'email_queue:scheduled')
 * @returns {Promise<string|null>} ID do email agendado ou null em caso de erro
 */
const scheduleEmail = async (
  emailData,
  scheduledDate,
  queueKey = "email_queue:scheduled"
) => {
  try {
    const emailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    const scheduledTime = new Date(scheduledDate).getTime();
    const emailKey = `${queueKey}:${emailId}`;

    // Preparar dados do email com metadados de segurança
    const enrichedEmail = {
      ...emailData,
      id: emailId,
      scheduled_date: scheduledDate,
      scheduled_timestamp: scheduledTime,
      created_at: new Date().toISOString(),
      status: "scheduled",
      attempts: 0,
      redis_key: emailKey,
      system: "email_queue_v2", // Identificador do novo sistema
    };

    // Usar saveScheduledMessage existente (compatibilidade)
    const success = await saveScheduledMessage(
      queueKey,
      scheduledTime,
      emailKey,
      enrichedEmail
    );

    if (success) {
      console.log("EMAIL_QUEUE > SCHEDULE > SUCCESS", {
        emailId,
        to: emailData.to,
        scheduledDate,
        queueKey,
      });
      return emailId;
    } else {
      throw new Error("Failed to save email to Redis");
    }
  } catch (error) {
    console.error("EMAIL_QUEUE > SCHEDULE > ERROR", {
      error: error.message,
      emailData: { to: emailData.to, subject: emailData.subject },
      scheduledDate,
    });
    return null;
  }
};

/**
 * Obtém emails agendados para o sistema de email queue
 *
 * @param {number} limit - Limite de emails a buscar
 * @param {string} queueKey - Chave da fila
 * @returns {Promise<Array>} Lista de emails prontos
 */
const getScheduledEmails = async (
  limit = 100,
  queueKey = "email_queue:scheduled"
) => {
  const now = Date.now();
  return await getScheduledMessages(queueKey, now, "EMAIL_QUEUE", {
    limit,
    remove: false,
  });
};

/**
 * Remove email processado (compatível com ambos os sistemas)
 *
 * @param {string} emailKey - Chave do email
 * @param {string} queueKey - Chave da fila
 * @returns {Promise<boolean>} Sucesso da remoção
 */
const removeProcessedEmail = async (
  emailKey,
  queueKey = "email_queue:scheduled"
) => {
  return await removeMessage(emailKey, queueKey);
};

/**
 * Obtém estatísticas das filas de email
 *
 * @returns {Promise<Object>} Estatísticas das filas
 */
const getEmailQueueStats = async () => {
  try {
    const client = await getRedisClient();
    if (!client) {
      return { error: "Redis client not available" };
    }

    // Contar emails em diferentes filas
    const pipeline = client.multi();
    pipeline.zCard("email_queue:scheduled");
    pipeline.zCard("email_queue:retry");
    pipeline.zCard("email_queue:dlq");
    pipeline.zCard("shotx:scheduled_messages"); // Compatibilidade com shotx

    const results = await pipeline.exec();

    return {
      emailQueue: {
        scheduled: (results[0] && results[0][1]) || 0,
        retry: (results[1] && results[1][1]) || 0,
        deadLetter: (results[2] && results[2][1]) || 0,
      },
      shotxQueue: {
        scheduled: (results[3] && results[3][1]) || 0,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("EMAIL_QUEUE > STATS > ERROR", error);
    return { error: error.message };
  }
};

/**
 * Testa a conectividade e performance do Redis
 *
 * @returns {Promise<Object>} Resultado do teste
 */
const testRedisPerformance = async () => {
  try {
    const client = await getRedisClient();
    if (!client) {
      return { success: false, error: "Redis client not available" };
    }

    const startTime = Date.now();

    // Teste de ping
    const pingResult = await client.ping();
    const pingTime = Date.now() - startTime;

    // Teste de operações básicas
    const testKey = `test:performance:${Date.now()}`;
    const setStart = Date.now();
    await client.set(testKey, "test_value", { EX: 10 });
    const setTime = Date.now() - setStart;

    const getStart = Date.now();
    const value = await client.get(testKey);
    const getTime = Date.now() - getStart;

    await client.del(testKey);

    return {
      success: true,
      ping: pingResult,
      performance: {
        ping: `${pingTime}ms`,
        set: `${setTime}ms`,
        get: `${getTime}ms`,
        total: `${Date.now() - startTime}ms`,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

module.exports = {
  // Funções originais (compatibilidade com shotxCron)
  getRedisClient,
  saveMessage,
  saveScheduledMessage,
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
  diagnoseRedisIntegrity,
  cleanupOrphanedKeys,

  // ✅ NOVAS FUNÇÕES PARA SISTEMA DE EMAIL ESCALÁVEL
  scheduleEmail,
  getScheduledEmails,
  removeProcessedEmail,
  getEmailQueueStats,
  testRedisPerformance,

  // Configuração segura
  getSecureRedisConfig,
};
