#!/usr/bin/env node

/**
 * Script de Migração - Configurações Redis Seguras
 * 
 * Este script ajuda a migrar configurações do .env para Firebase Config
 * de forma segura, seguindo as correções implementadas na Task 2.
 * 
 * USO:
 * node functions/emailSystem/scripts/migrate-redis-config.js
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 MIGRAÇÃO REDIS - Configurações Seguras v2.0');
console.log('================================================\n');

/**
 * Lê configurações do arquivo .env
 */
function readEnvConfig() {
  const envPath = path.join(__dirname, '../../../.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('⚠️  Arquivo .env não encontrado em:', envPath);
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const config = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      config[key.trim()] = value.trim().replace(/"/g, '');
    }
  });

  return config;
}

/**
 * Gera comandos Firebase Config
 */
function generateFirebaseCommands(envConfig) {
  const commands = [];
  
  console.log('📋 COMANDOS PARA MIGRAÇÃO:\n');
  console.log('Execute os comandos abaixo para migrar suas configurações:\n');

  // Configurações Redis
  if (envConfig.REDIS_HOST) {
    const cmd = `firebase functions:config:set redis.host="${envConfig.REDIS_HOST}"`;
    commands.push(cmd);
    console.log(`✅ Redis Host: ${cmd}`);
  }

  if (envConfig.REDIS_PORT) {
    const cmd = `firebase functions:config:set redis.port="${envConfig.REDIS_PORT}"`;
    commands.push(cmd);
    console.log(`✅ Redis Port: ${cmd}`);
  }

  if (envConfig.REDIS_PASSWORD) {
    const cmd = `firebase functions:config:set redis.password="${envConfig.REDIS_PASSWORD}"`;
    commands.push(cmd);
    console.log(`🔐 Redis Password: ${cmd}`);
  }

  if (envConfig.REDIS_DATABASE) {
    const cmd = `firebase functions:config:set redis.database="${envConfig.REDIS_DATABASE}"`;
    commands.push(cmd);
    console.log(`✅ Redis Database: ${cmd}`);
  }

  // Configurações SSL (recomendado para produção)
  const sslCmd = `firebase functions:config:set redis.ssl="true"`;
  commands.push(sslCmd);
  console.log(`🔒 Redis SSL: ${sslCmd}`);

  // Configurações de timeout
  const timeoutCmd = `firebase functions:config:set redis.connect_timeout="10000"`;
  commands.push(timeoutCmd);
  console.log(`⏱️  Redis Timeout: ${timeoutCmd}`);

  const commandTimeoutCmd = `firebase functions:config:set redis.command_timeout="5000"`;
  commands.push(commandTimeoutCmd);
  console.log(`⏱️  Redis Command Timeout: ${commandTimeoutCmd}`);

  return commands;
}

/**
 * Gera script de migração
 */
function generateMigrationScript(commands) {
  const scriptContent = `#!/bin/bash

# Script de Migração - Configurações Redis Seguras
# Gerado automaticamente em: ${new Date().toISOString()}

echo "🔧 Iniciando migração das configurações Redis..."

${commands.join('\n')}

echo ""
echo "✅ Migração concluída!"
echo ""
echo "📝 PRÓXIMOS PASSOS:"
echo "1. Execute: firebase deploy --only functions"
echo "2. Remova as configurações do arquivo .env após confirmar que tudo funciona"
echo "3. Teste a conectividade com: node functions/emailSystem/scripts/test-redis.js"
echo ""
echo "🔒 SEGURANÇA:"
echo "- As configurações agora estão seguras no Firebase Config"
echo "- SSL/TLS foi habilitado para conexões seguras"
echo "- Timeouts otimizados para melhor performance"
echo ""
`;

  const scriptPath = path.join(__dirname, 'migrate-redis.sh');
  fs.writeFileSync(scriptPath, scriptContent);
  fs.chmodSync(scriptPath, '755');

  console.log(`\n📄 Script gerado: ${scriptPath}`);
  console.log('Execute: ./functions/emailSystem/scripts/migrate-redis.sh\n');
}

/**
 * Valida configurações atuais
 */
function validateCurrentConfig(envConfig) {
  console.log('🔍 VALIDAÇÃO DAS CONFIGURAÇÕES ATUAIS:\n');

  const issues = [];

  if (!envConfig.REDIS_HOST) {
    issues.push('❌ REDIS_HOST não configurado');
  } else {
    console.log(`✅ REDIS_HOST: ${envConfig.REDIS_HOST}`);
  }

  if (!envConfig.REDIS_PASSWORD) {
    issues.push('⚠️  REDIS_PASSWORD não configurado (inseguro para produção)');
  } else {
    console.log(`🔐 REDIS_PASSWORD: ****** (configurado)`);
  }

  if (!envConfig.REDIS_PORT) {
    console.log('ℹ️  REDIS_PORT: 6379 (padrão)');
  } else {
    console.log(`✅ REDIS_PORT: ${envConfig.REDIS_PORT}`);
  }

  if (!envConfig.REDIS_DATABASE) {
    console.log('ℹ️  REDIS_DATABASE: 0 (padrão)');
  } else {
    console.log(`✅ REDIS_DATABASE: ${envConfig.REDIS_DATABASE}`);
  }

  if (!envConfig.REDIS_SSL) {
    issues.push('⚠️  REDIS_SSL não configurado (recomendado para produção)');
  }

  if (issues.length > 0) {
    console.log('\n⚠️  PROBLEMAS ENCONTRADOS:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }

  console.log('\n');
  return issues.length === 0;
}

/**
 * Função principal
 */
function main() {
  try {
    // Ler configurações atuais
    const envConfig = readEnvConfig();
    
    // Validar configurações
    const isValid = validateCurrentConfig(envConfig);
    
    // Gerar comandos de migração
    const commands = generateFirebaseCommands(envConfig);
    
    // Gerar script de migração
    generateMigrationScript(commands);
    
    console.log('🎯 BENEFÍCIOS DA MIGRAÇÃO:');
    console.log('- ✅ Configurações seguras (não expostas no código)');
    console.log('- 🔒 SSL/TLS habilitado para conexões seguras');
    console.log('- ⚡ Performance otimizada com connection pooling');
    console.log('- 🔄 Estratégia de reconexão melhorada');
    console.log('- 📊 Observabilidade aprimorada');
    console.log('- 🛡️  Validação rigorosa de configurações\n');

    console.log('📚 DOCUMENTAÇÃO:');
    console.log('- Configuração: functions/emailSystem/README.md');
    console.log('- Troubleshooting: functions/emailSystem/docs/troubleshooting.md\n');

    if (!isValid) {
      console.log('⚠️  ATENÇÃO: Corrija os problemas identificados antes de prosseguir.\n');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = {
  readEnvConfig,
  generateFirebaseCommands,
  validateCurrentConfig
};
