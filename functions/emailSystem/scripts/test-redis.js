#!/usr/bin/env node

/**
 * Script de Teste - Configuração Redis Segura
 * 
 * Testa a conectividade e performance do Redis com as novas
 * configurações seguras implementadas na Task 2.
 * 
 * USO:
 * node functions/emailSystem/scripts/test-redis.js
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

const path = require('path');

// Configurar path para módulos do projeto
const projectRoot = path.join(__dirname, '../../..');
process.chdir(projectRoot);

const { 
  getRedisClient, 
  testRedisPerformance, 
  getEmailQueueStats,
  getSecureRedisConfig 
} = require('../utils/redisClient');

console.log('🧪 TESTE REDIS - Configurações Seguras v2.0');
console.log('=============================================\n');

/**
 * Testa configuração segura
 */
async function testSecureConfig() {
  console.log('🔧 1. TESTANDO CONFIGURAÇÃO SEGURA...\n');
  
  try {
    const config = getSecureRedisConfig();
    
    console.log('✅ Configuração carregada com sucesso:');
    console.log(`   Host: ${config.socket.host}`);
    console.log(`   Port: ${config.socket.port}`);
    console.log(`   Database: ${config.database}`);
    console.log(`   SSL/TLS: ${config.socket.tls ? '✅ Habilitado' : '❌ Desabilitado'}`);
    console.log(`   Password: ${config.password ? '🔐 Configurado' : '⚠️  Não configurado'}`);
    console.log(`   Connection Timeout: ${config.connectTimeout}ms`);
    console.log(`   Command Timeout: ${config.commandTimeout}ms\n`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro na configuração:', error.message);
    console.log('\n💡 SOLUÇÕES:');
    console.log('1. Execute o script de migração: node functions/emailSystem/scripts/migrate-redis-config.js');
    console.log('2. Configure manualmente: firebase functions:config:set redis.host="your-host"');
    console.log('3. Verifique se o Firebase CLI está configurado corretamente\n');
    return false;
  }
}

/**
 * Testa conectividade básica
 */
async function testConnectivity() {
  console.log('🔌 2. TESTANDO CONECTIVIDADE...\n');
  
  try {
    const client = await getRedisClient();
    
    if (!client) {
      throw new Error('Falha ao obter cliente Redis');
    }
    
    console.log('✅ Conexão estabelecida com sucesso');
    console.log(`   Status: ${client.isOpen ? 'Conectado' : 'Desconectado'}`);
    console.log(`   Ready: ${client.isReady ? 'Pronto' : 'Não pronto'}\n`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro de conectividade:', error.message);
    console.log('\n💡 VERIFICAÇÕES:');
    console.log('1. Verifique se o servidor Redis está rodando');
    console.log('2. Confirme host e porta nas configurações');
    console.log('3. Teste conectividade de rede');
    console.log('4. Verifique credenciais de autenticação\n');
    return false;
  }
}

/**
 * Testa performance
 */
async function testPerformance() {
  console.log('⚡ 3. TESTANDO PERFORMANCE...\n');
  
  try {
    const result = await testRedisPerformance();
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    console.log('✅ Teste de performance concluído:');
    console.log(`   Ping: ${result.performance.ping}`);
    console.log(`   Set Operation: ${result.performance.set}`);
    console.log(`   Get Operation: ${result.performance.get}`);
    console.log(`   Total Time: ${result.performance.total}\n`);
    
    // Avaliar performance
    const pingTime = parseInt(result.performance.ping);
    const totalTime = parseInt(result.performance.total);
    
    if (pingTime < 10) {
      console.log('🚀 Performance: EXCELENTE (< 10ms)');
    } else if (pingTime < 50) {
      console.log('✅ Performance: BOA (< 50ms)');
    } else if (pingTime < 100) {
      console.log('⚠️  Performance: ACEITÁVEL (< 100ms)');
    } else {
      console.log('❌ Performance: RUIM (> 100ms) - Verifique conectividade');
    }
    
    console.log('');
    return true;
    
  } catch (error) {
    console.error('❌ Erro no teste de performance:', error.message);
    return false;
  }
}

/**
 * Testa operações de fila
 */
async function testQueueOperations() {
  console.log('📋 4. TESTANDO OPERAÇÕES DE FILA...\n');
  
  try {
    const stats = await getEmailQueueStats();
    
    if (stats.error) {
      throw new Error(stats.error);
    }
    
    console.log('✅ Estatísticas das filas:');
    console.log('   Email Queue:');
    console.log(`     Scheduled: ${stats.emailQueue.scheduled}`);
    console.log(`     Retry: ${stats.emailQueue.retry}`);
    console.log(`     Dead Letter: ${stats.emailQueue.deadLetter}`);
    console.log('   ShotX Queue:');
    console.log(`     Scheduled: ${stats.shotxQueue.scheduled}`);
    console.log(`   Timestamp: ${stats.timestamp}\n`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro nas operações de fila:', error.message);
    return false;
  }
}

/**
 * Testa operações avançadas
 */
async function testAdvancedOperations() {
  console.log('🔬 5. TESTANDO OPERAÇÕES AVANÇADAS...\n');
  
  try {
    const client = await getRedisClient();
    
    // Teste de pipeline
    console.log('📦 Testando pipeline operations...');
    const pipeline = client.multi();
    pipeline.set('test:pipeline:1', 'value1');
    pipeline.set('test:pipeline:2', 'value2');
    pipeline.get('test:pipeline:1');
    pipeline.get('test:pipeline:2');
    pipeline.del('test:pipeline:1');
    pipeline.del('test:pipeline:2');
    
    const results = await pipeline.exec();
    console.log(`✅ Pipeline executado: ${results.length} operações\n`);
    
    // Teste de sorted set (usado para agendamento)
    console.log('📊 Testando sorted set operations...');
    const testKey = `test:sortedset:${Date.now()}`;
    const now = Date.now();
    
    await client.zAdd(testKey, [
      { score: now + 1000, value: 'item1' },
      { score: now + 2000, value: 'item2' },
      { score: now + 3000, value: 'item3' }
    ]);
    
    const count = await client.zCard(testKey);
    const items = await client.zRange(testKey, 0, -1);
    
    await client.del(testKey);
    
    console.log(`✅ Sorted set: ${count} items adicionados e removidos`);
    console.log(`   Items: ${items.join(', ')}\n`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Erro nas operações avançadas:', error.message);
    return false;
  }
}

/**
 * Função principal
 */
async function main() {
  const tests = [
    { name: 'Configuração Segura', fn: testSecureConfig },
    { name: 'Conectividade', fn: testConnectivity },
    { name: 'Performance', fn: testPerformance },
    { name: 'Operações de Fila', fn: testQueueOperations },
    { name: 'Operações Avançadas', fn: testAdvancedOperations }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const success = await test.fn();
      results.push({ name: test.name, success });
    } catch (error) {
      console.error(`❌ Erro no teste ${test.name}:`, error.message);
      results.push({ name: test.name, success: false });
    }
  }
  
  // Resumo dos resultados
  console.log('📊 RESUMO DOS TESTES:');
  console.log('====================\n');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });
  
  console.log(`\n🎯 RESULTADO: ${passed}/${total} testes passaram\n`);
  
  if (passed === total) {
    console.log('🎉 PARABÉNS! Todas as configurações Redis estão funcionando perfeitamente!');
    console.log('✅ O sistema está pronto para produção com configurações seguras.\n');
  } else {
    console.log('⚠️  ATENÇÃO: Alguns testes falharam. Verifique as configurações.');
    console.log('💡 Execute o script de migração se necessário.\n');
    process.exit(1);
  }
  
  // Fechar conexão
  try {
    const client = await getRedisClient();
    if (client) {
      await client.quit();
      console.log('🔌 Conexão Redis fechada com sucesso.');
    }
  } catch (error) {
    console.log('⚠️  Aviso: Erro ao fechar conexão Redis:', error.message);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erro fatal:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testSecureConfig,
  testConnectivity,
  testPerformance,
  testQueueOperations,
  testAdvancedOperations
};
